<template>
  <div class="k-line-chart-container">
    <div class="chart-header">
      <div class="chart-title">K线图</div>
      <div class="chart-close" @click="closeChart">
        <i class="el-icon-close"></i>
      </div>
    </div>
    <div id="chart" ref="kLineChart" class="k-line-chart"></div>
    <div class="indicators-panel">
      <!-- 显示当前可见的指标 -->
      <div v-if="indicators.MA.visible" class="indicator-item">
        <span class="indicator-name">MA</span>
        <i
          class="el-icon-close indicator-close"
          @click="closeIndicator('MA')"
        ></i>
      </div>
      <div v-if="indicators.VOL.visible" class="indicator-item">
        <span class="indicator-name">VOL</span>
        <i
          class="el-icon-close indicator-close"
          @click="closeIndicator('VOL')"
        ></i>
      </div>
      <div v-if="indicators.KDJ.visible" class="indicator-item">
        <span class="indicator-name">KDJ</span>
        <i
          class="el-icon-close indicator-close"
          @click="closeIndicator('KDJ')"
        ></i>
      </div>
      <div
        v-if="indicators.FISHING_SEASON_TDX.visible"
        class="indicator-item fishing-season"
      >
        <span class="indicator-name">🎣 捕捞季节(通达信)</span>
        <i
          class="el-icon-close indicator-close"
          @click="closeIndicator('FISHING_SEASON_TDX')"
        ></i>
      </div>

      <!-- 添加指标按钮 -->
      <div v-if="hasHiddenIndicators" class="add-indicator-dropdown">
        <el-dropdown trigger="click" @command="openIndicator">
          <span class="add-indicator-btn">
            <i class="el-icon-plus"></i>
            添加指标
          </span>
          <el-dropdown-menu slot="dropdown">
            <el-dropdown-item v-if="!indicators.MA.visible" command="MA">
              MA (移动平均线)
            </el-dropdown-item>
            <el-dropdown-item v-if="!indicators.VOL.visible" command="VOL">
              VOL (成交量)
            </el-dropdown-item>
            <el-dropdown-item v-if="!indicators.KDJ.visible" command="KDJ">
              KDJ (随机指标)
            </el-dropdown-item>
            <el-dropdown-item
              v-if="!indicators.FISHING_SEASON_TDX.visible"
              command="FISHING_SEASON_TDX"
            >
              🎣 捕捞季节(通达信)
            </el-dropdown-item>
          </el-dropdown-menu>
        </el-dropdown>
      </div>
    </div>
  </div>
</template>

<script>
  import { init, dispose, registerIndicator } from 'klinecharts'

  export default {
    name: 'KLineChart',
    props: {
      symbol: {
        type: String,
        default: '000001.SZ',
      },
      interval: {
        type: String,
        default: '1d',
      },
    },
    data() {
      return {
        chart: null,
        data: [],
        indicators: {
          MA: { visible: true, paneId: 'candle_pane' },
          VOL: { visible: false, paneId: 'vol_pane' },
          KDJ: { visible: false, paneId: 'kdj_pane' },
          FISHING_SEASON_TDX: { visible: true, paneId: 'fishing_pane' },
        },
      }
    },
    computed: {
      hasHiddenIndicators() {
        return Object.values(this.indicators).some(
          (indicator) => !indicator.visible
        )
      },
    },
    mounted() {
      this.initChart()
      this.loadData()
    },
    beforeDestroy() {
      if (this.chart) {
        dispose(this.chart)
      }
    },
    methods: {
      // 注册通达信版本的捕捞季节指标
      registerFishingSeasonIndicator() {
        // 注册通达信版本的捕捞季节指标
        registerIndicator({
          name: 'FISHING_SEASON_TDX',
          shortName: '捕捞季节',
          precision: 2,
          calcParams: [3, 13], // EMA周期, MA周期
          shouldOhlc: false,
          shouldFormatBigNumber: false,
          visible: true,
          zLevel: 0,
          series: 'normal',
          figures: [
            {
              key: 'xys0',
              title: 'XYS0',
              type: 'bar',
              baseValue: 0,
              styles: {
                upColor: '#ef5350', // 0轴上方红色
                downColor: '#26a69a', // 0轴下方绿色
                noChangeColor: '#888888', // 平盘灰色
                upBorderColor: '#ef5350',
                downBorderColor: '#26a69a',
                noChangeBorderColor: '#888888',
              },
            },
            {
              key: 'xys1',
              title: 'XYS1',
              type: 'line',
              styles: () => ({
                color: '#FF6B35', // 橙红色快线
                size: 1,
              }),
            },
            {
              key: 'xys2',
              title: 'XYS2',
              type: 'line',
              styles: () => ({
                color: '#4ECDC4', // 青色慢线
                size: 1,
              }),
            },
            {
              key: 'zzx',
              title: 'ZZX',
              type: 'line',
              styles: () => ({
                color: '#888888', // 灰色零轴线，更加明显
                size: 1,
                style: 'dashed', // 虚线样式
              }),
            },
            {
              key: 'signal1',
              title: '强烈买入',
              type: 'bar',
              baseValue: 0,
              styles: () => ({
                color: '#00C851', // 鲜绿色 - 强烈买入信号
                borderColor: '#00C851',
              }),
            },
            {
              key: 'signal2',
              title: '买入信号',
              type: 'bar',
              baseValue: 0,
              styles: () => ({
                color: '#FFB300', // 橙黄色 - 买入信号
                borderColor: '#FFB300',
              }),
            },
            {
              key: 'signal3',
              title: '关注信号',
              type: 'bar',
              baseValue: 0,
              styles: () => ({
                color: '#33B5E5', // 天蓝色 - 关注信号
                borderColor: '#33B5E5',
              }),
            },
            {
              key: 'signal4',
              title: '弱买入',
              type: 'bar',
              baseValue: 0,
              styles: () => ({
                color: '#AA66CC', // 紫色 - 弱买入信号
                borderColor: '#AA66CC',
              }),
            },
          ],
          minValue: -10,
          maxValue: 10,
          calc: (kLineDataList, indicator) => {
            const { calcParams } = indicator
            const [emaPeriod, maPeriod] = calcParams
            const result = []

            if (kLineDataList.length < Math.max(emaPeriod * 3, maPeriod)) {
              return result
            }

            // 内部EMA计算函数
            const calculateEMA = (values, period) => {
              const emaValues = []
              const multiplier = 2 / (period + 1)
              let ema = null

              for (let i = 0; i < values.length; i++) {
                const value = values[i]

                if (value === null || value === undefined) {
                  emaValues.push(null)
                  continue
                }

                if (ema === null) {
                  ema = value
                } else {
                  ema = (value - ema) * multiplier + ema
                }

                emaValues.push(ema)
              }

              return emaValues
            }

            // 辅助函数：计算MA
            const calculateMA = (values, period) => {
              const maValues = []

              for (let i = 0; i < values.length; i++) {
                if (i < period - 1) {
                  maValues.push(null)
                } else {
                  const sum = values
                    .slice(i - period + 1, i + 1)
                    .reduce((a, b) => a + (b || 0), 0)
                  maValues.push(sum / period)
                }
              }

              return maValues
            }

            // 辅助函数：获取前一个值 (REF函数)
            const ref = (values, period) => {
              return values.map((_, i) =>
                i >= period ? values[i - period] : null
              )
            }

            // 按照通达信公式计算
            // WY1001:=(2*CLOSE+HIGH+LOW)/4; (修正公式)
            const wy1001 = kLineDataList.map(
              (item) => (2 * item.close + item.high + item.low) / 4
            )

            // WY1002:=EMA(WY1001,3);
            const wy1002 = calculateEMA(wy1001, emaPeriod)

            // WY1003:=EMA(WY1002,3);
            const wy1003 = calculateEMA(wy1002, emaPeriod)

            // WY1004:=EMA(WY1003,3);
            const wy1004 = calculateEMA(wy1003, emaPeriod)

            // XYS0:(WY1004-REF(WY1004,1))/REF(WY1004,1)*100;
            const wy1004Ref = ref(wy1004, 1)
            const xys0 = wy1004.map((val, i) => {
              const refVal = wy1004Ref[i]
              if (val === null || refVal === null || refVal === 0) return null
              return ((val - refVal) / refVal) * 100
            })

            // PJGJ:=AMOUNT/VOL/100; (保留以保持与通达信公式一致性)
            // const pjgj = kLineDataList.map((item) =>
            //   item.volume > 0
            //     ? (item.turnover || item.amount || 0) / item.volume / 100
            //     : 0
            // )

            // SSRYDJX:=MA(PJGJ,13); (保留以保持与通达信公式一致性)
            // const ssrydjx = calculateMA(pjgj, maPeriod)

            // SSRCJL:=EMA(VOL,13);
            const volumes = kLineDataList.map((item) => item.volume)
            const ssrcjl = calculateEMA(volumes, maPeriod)

            // SSRCJE:=EMA(AMOUNT,13);
            const amounts = kLineDataList.map(
              (item) => item.turnover || item.amount || 0
            )
            const ssrcje = calculateEMA(amounts, maPeriod)

            // SSRCBJX:=SSRCJE/SSRCJL/100;
            const ssrcbjx = ssrcje.map((amount, i) => {
              const vol = ssrcjl[i]
              return vol > 0 ? amount / vol / 100 : 0
            })

            // CYS13:=(CLOSE-SSRCBJX)/SSRCBJX*100;
            const cys13 = kLineDataList.map((item, i) => {
              const bjx = ssrcbjx[i]
              return bjx > 0 ? ((item.close - bjx) / bjx) * 100 : 0
            })

            // XYSHSL:=EMA(VOL/CAPITAL*100,13);
            // 注意：CAPITAL在实际使用中需要替换为实际的流通股本，这里用一个估算值
            const capital = 1000000000 // 10亿股，实际应该从数据中获取
            const hsls = kLineDataList.map(
              (item) => (item.volume / capital) * 100
            )
            const xyshsl = calculateEMA(hsls, maPeriod)

            // XYS1:MA(XYS0,1); XYS2:MA(XYS0,2);
            const xys1 = calculateMA(xys0, 1)
            const xys2 = calculateMA(xys0, 2)

            for (let i = 0; i < kLineDataList.length; i++) {
              const hsslVal = xyshsl[i] || 0
              const cysVal = cys13[i] || 0

              result.push({
                xys0: xys0[i],
                xys1: xys1[i],
                xys2: xys2[i],
                zzx: 0, // 零轴线
                signal1: hsslVal > 6.1 && cysVal > 5 ? 2 : null,
                signal2: hsslVal > 3.8 && cysVal > 5 ? 1.5 : null,
                signal3: hsslVal > 2.1 && cysVal > 5 ? 1 : null,
                signal4: hsslVal > 1.8 && cysVal > 5 ? 0.5 : null,
              })
            }

            return result
          },
          createTooltipDataSource: (params) => {
            const { indicator, visibleDataList, crosshair } = params
            const data = visibleDataList[crosshair.dataIndex]

            if (!data || !data.indicators) return null

            const indicatorData = data.indicators[indicator.name]
            if (!indicatorData) return null

            return {
              name: '捕捞季节(通达信)',
              calcParamsText: `(${indicator.calcParams.join(', ')})`,
              legends: [
                {
                  title: 'XYS0(主力线)',
                  value: indicatorData.xys0
                    ? (indicatorData.xys0 >= 0 ? '🔴' : '🟢') +
                      indicatorData.xys0.toFixed(2) +
                      '%'
                    : '--',
                },
                {
                  title: 'XYS1(快线)',
                  value: indicatorData.xys1
                    ? '🟠' + indicatorData.xys1.toFixed(2) + '%'
                    : '--',
                },
                {
                  title: 'XYS2(慢线)',
                  value: indicatorData.xys2
                    ? '🔵' + indicatorData.xys2.toFixed(2) + '%'
                    : '--',
                },
              ],
            }
          },
        })
      },

      // RSI计算方法
      calculateRSI(kLineDataList, period) {
        const rsiValues = []
        let gains = []
        let losses = []

        for (let i = 1; i < kLineDataList.length; i++) {
          const change = kLineDataList[i].close - kLineDataList[i - 1].close
          gains.push(change > 0 ? change : 0)
          losses.push(change < 0 ? Math.abs(change) : 0)

          if (i >= period) {
            const avgGain =
              gains.slice(-period).reduce((a, b) => a + b, 0) / period
            const avgLoss =
              losses.slice(-period).reduce((a, b) => a + b, 0) / period

            if (avgLoss === 0) {
              rsiValues.push(100)
            } else {
              const rs = avgGain / avgLoss
              const rsi = 100 - 100 / (1 + rs)
              rsiValues.push(rsi)
            }
          } else {
            rsiValues.push(null)
          }
        }

        return [null, ...rsiValues] // 第一个值为null，因为没有前一个值来计算变化
      },

      // EMA计算方法
      calculateEMA(values, period) {
        const emaValues = []
        const multiplier = 2 / (period + 1)
        let ema = null

        for (let i = 0; i < values.length; i++) {
          const value = values[i]

          if (value === null || value === undefined) {
            emaValues.push(null)
            continue
          }

          if (ema === null) {
            ema = value
          } else {
            ema = (value - ema) * multiplier + ema
          }

          emaValues.push(ema)
        }

        return emaValues
      },

      initChart() {
        // 注册自定义指标
        this.registerFishingSeasonIndicator()
        this.chart = init(this.$refs.kLineChart, {
          styles: {
            grid: {
              show: false,
              // 网格水平线
              horizontal: {
                show: false,
                size: 1,
                color: '#989fb1',
                style: 'solid',
                dashValue: [2, 2],
              },
              // 网格垂直线
              vertical: {
                show: false,
                size: 1,
                color: '#989fb1',
                style: 'solid',
                dashValue: [2, 2],
              },
            },
            candle: {
              priceMark: {
                high: {
                  color: '#FFFFFF',
                },
                low: {
                  color: '#FFFFFF',
                },
              },
              // 蜡烛图上下间距，大于1为绝对值，大于0小余1则为比例
              margin: {
                top: 0.2,
                bottom: 0.1,
              },
              // 蜡烛图类型 'candle_solid'|'candle_stroke'|'candle_up_stroke'|'candle_down_stroke'|'ohlc'|'area'
              type: 'candle_solid',
              // 蜡烛图颜色配置：红涨绿跌
              bar: {
                upColor: '#ef5350', // 红色（上涨）
                downColor: '#26a69a', // 绿色（下跌）
                noChangeColor: '#888888', // 平盘灰色
                upBorderColor: '#ef5350', // 红色边框
                downBorderColor: '#26a69a', // 绿色边框
                noChangeBorderColor: '#888888', // 平盘灰色边框
                upWickColor: '#ef5350', // 红色芯线
                downWickColor: '#26a69a', // 绿色芯线
                noChangeWickColor: '#888888', // 平盘灰色芯线
              },
              area: {
                lineColor: '#FFFFFF',
                style: 'fill',
              },
            },
            yAxis: {
              show: true,
              axisLine: {
                show: true,
                color: '#989fb1',
                size: 1,
              },
            },
            xAxis: {
              show: true,
              axisLine: {
                show: true,
                color: '#989fb1',
                size: 1,
              },
            },
            separator: {
              size: 1,
              color: '#989fb1',
              fill: true,
            },
            // 指标样式配置：红涨绿跌（仅适用于标准指标）
            indicator: {
              bars: [
                {
                  upColor: '#ef5350', // 红色（上涨）
                  downColor: '#26a69a', // 绿色（下跌）
                  noChangeColor: '#888888', // 平盘灰色
                },
              ],
              lines: [
                {
                  color: '#FF6B35', // K线颜色（橙红色）
                },
                {
                  color: '#4ECDC4', // D线颜色（青色）
                },
                {
                  color: '#45B7D1', // J线颜色（蓝色）
                },
                {
                  color: '#E11D74', // 第4条线颜色（粉红色）
                },
                {
                  color: '#01C5C4', // 第5条线颜色（青绿色）
                },
              ],
            },
          },
        })
        this.createIndicators()
        this.chart.setLocale('zh-CN')
        this.chart.applyNewData(this.data)
      },
      createIndicators() {
        // 创建所有可见的指标
        if (this.indicators.MA.visible) {
          this.chart.createIndicator('MA', false, {
            id: 'candle_pane',
          })
        }

        if (this.indicators.VOL.visible) {
          // 创建成交量指标，配置红涨绿跌颜色
          this.chart.createIndicator('VOL', true, {
            height: 80,
          })
        }

        if (this.indicators.KDJ.visible) {
          // 创建KDJ指标
          this.chart.createIndicator('KDJ', true, {
            height: 80,
          })
        }

        if (this.indicators.FISHING_SEASON_TDX.visible) {
          // 创建捕捞季节指标
          this.chart.createIndicator('FISHING_SEASON_TDX', true, {
            height: 120,
          })
        }
      },
      closeIndicator(indicatorName) {
        // 关闭指定的指标
        if (
          this.indicators[indicatorName] &&
          this.indicators[indicatorName].visible
        ) {
          // 更新指标状态为不可见
          this.$set(this.indicators[indicatorName], 'visible', false)

          // 使用 klinecharts API 移除指标，而不是重新初始化整个图表
          if (this.chart) {
            try {
              // 使用正确的 removeIndicator API
              const result = this.chart.removeIndicator({
                name: indicatorName,
              })

              if (!result) {
                console.warn(`移除指标 ${indicatorName} 失败，可能指标不存在`)
              }
            } catch (error) {
              console.warn(`移除指标 ${indicatorName} 时出错:`, error)
              // 如果 API 调用失败，回退到重新初始化的方法
              this.reinitializeChart()
            }
          }
        }
      },

      reinitializeChart() {
        // 重新初始化图表的备用方法
        if (this.chart) {
          // 保存当前数据
          const currentData = this.data

          // 销毁当前图表
          dispose(this.chart)

          // 重新初始化图表
          this.initChart()

          // 恢复数据
          if (currentData && currentData.length > 0) {
            this.chart.applyNewData(currentData)
          }
        }
      },
      async loadData() {
        try {
          // 模拟数据，实际使用时替换为真实API
          // 通过远程接口获取K线数据
          const response = await fetch(
            'https://klinecharts.com/datas/kline.json'
          )
          const remoteData = await response.json()
          this.data = remoteData
          this.chart.applyNewData(this.data)
        } catch (error) {
          console.error('加载K线数据失败:', error)
        }
      },
      closeChart() {
        // 触发关闭事件，让父组件处理关闭逻辑
        this.$emit('close')
      },

      // 添加重新打开指标的方法
      openIndicator(indicatorName) {
        if (
          this.indicators[indicatorName] &&
          !this.indicators[indicatorName].visible
        ) {
          // 更新指标状态为可见
          this.$set(this.indicators[indicatorName], 'visible', true)

          // 重新创建指标
          if (this.chart) {
            try {
              if (indicatorName === 'MA') {
                this.chart.createIndicator('MA', false, {
                  id: 'candle_pane',
                })
              } else if (indicatorName === 'VOL') {
                // 重新创建成交量指标，使用全局样式配置
                this.chart.createIndicator('VOL', true, {
                  height: 80,
                })
              } else if (indicatorName === 'KDJ') {
                // 重新创建KDJ指标，使用全局样式配置
                this.chart.createIndicator('KDJ', true, {
                  height: 80,
                })
              } else if (indicatorName === 'FISHING_SEASON_TDX') {
                // 重新创建捕捞季节指标
                this.chart.createIndicator('FISHING_SEASON_TDX', true, {
                  height: 120,
                })
              }
            } catch (error) {
              console.warn(`创建指标 ${indicatorName} 时出错:`, error)
            }
          }
        }
      },

      // 获取指标状态
      getIndicatorStatus() {
        return Object.keys(this.indicators).reduce((status, key) => {
          status[key] = this.indicators[key].visible
          return status
        }, {})
      },
    },
  }
</script>

<style lang="scss" scoped>
  .k-line-chart-container {
    display: flex;
    flex-direction: column;
    width: 100%;
    height: 500px; /* 高度增加一倍 */
    background-color: #fff;
    border-radius: 4px;

    .chart-header {
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 8px 12px;
      border-bottom: 1px solid #eee;

      .chart-title {
        font-size: 14px;
        font-weight: 500;
      }

      .chart-close {
        color: #909399;
        cursor: pointer;
        &:hover {
          color: #409eff;
        }
      }
    }

    .k-line-chart {
      flex: 1;
      width: 100%;
    }

    .indicators-panel {
      display: flex;
      flex-wrap: wrap;
      padding: 8px;
      border-top: 1px solid #eee;

      .indicator-item {
        display: flex;
        align-items: center;
        padding: 4px 8px;
        margin-right: 12px;
        background-color: #f5f7fa;
        border-radius: 4px;

        .indicator-name {
          margin-right: 4px;
          font-size: 12px;
        }

        .indicator-close {
          font-size: 12px;
          color: #909399;
          cursor: pointer;
          &:hover {
            color: #f56c6c;
          }
        }
      }

      .fishing-season {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        border: 1px solid #667eea;

        .indicator-name {
          font-weight: 500;
        }

        .indicator-close {
          color: rgba(255, 255, 255, 0.8);
          &:hover {
            color: #fff;
          }
        }
      }

      .add-indicator-dropdown {
        .add-indicator-btn {
          display: flex;
          align-items: center;
          padding: 4px 8px;
          background-color: #e6f7ff;
          border: 1px dashed #409eff;
          border-radius: 4px;
          color: #409eff;
          font-size: 12px;
          cursor: pointer;
          transition: all 0.3s;

          i {
            margin-right: 4px;
          }

          &:hover {
            background-color: #409eff;
            color: #fff;
            border-color: #409eff;
          }
        }
      }
    }
  }
</style>
