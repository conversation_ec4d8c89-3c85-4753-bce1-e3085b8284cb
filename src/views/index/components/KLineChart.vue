<template>
  <div class="k-line-chart-container">
    <div class="chart-header">
      <div class="chart-title">K线图</div>
      <div class="chart-close" @click="closeChart">
        <i class="el-icon-close"></i>
      </div>
    </div>
    <div id="chart" ref="kLineChart" class="k-line-chart"></div>
    <div class="indicators-panel">
      <div v-if="indicators.MA.visible" class="indicator-item">
        <span class="indicator-name">MA</span>
        <i
          class="el-icon-close indicator-close"
          @click="closeIndicator('MA')"
        ></i>
      </div>
      <div v-if="indicators.VOL.visible" class="indicator-item">
        <span class="indicator-name">VOL</span>
        <i
          class="el-icon-close indicator-close"
          @click="closeIndicator('VOL')"
        ></i>
      </div>
      <div v-if="indicators.KDJ.visible" class="indicator-item">
        <span class="indicator-name">KDJ</span>
        <i
          class="el-icon-close indicator-close"
          @click="closeIndicator('KDJ')"
        ></i>
      </div>
    </div>
  </div>
</template>

<script>
  import { init, dispose } from 'klinecharts'

  export default {
    name: 'KLineChart',
    props: {
      symbol: {
        type: String,
        default: '000001.SZ',
      },
      interval: {
        type: String,
        default: '1d',
      },
    },
    data() {
      return {
        chart: null,
        data: [],
        indicators: {
          MA: { visible: true, paneId: 'candle_pane' },
          VOL: { visible: true, paneId: 'vol_pane' },
          KDJ: { visible: true, paneId: 'kdj_pane' },
        },
      }
    },
    mounted() {
      this.initChart()
      this.loadData()
    },
    beforeDestroy() {
      if (this.chart) {
        dispose(this.chart)
      }
    },
    methods: {
      initChart() {
        this.chart = init(this.$refs.kLineChart, {
          styles: {
            grid: {
              show: false,
              // 网格水平线
              horizontal: {
                show: false,
                size: 1,
                color: '#989fb1',
                style: 'solid',
                dashValue: [2, 2],
              },
              // 网格垂直线
              vertical: {
                show: false,
                size: 1,
                color: '#989fb1',
                style: 'solid',
                dashValue: [2, 2],
              },
            },
            candle: {
              priceMark: {
                high: {
                  color: '#FFFFFF',
                },
                low: {
                  color: '#FFFFFF',
                },
              },
              // 蜡烛图上下间距，大于1为绝对值，大于0小余1则为比例
              margin: {
                top: 0.2,
                bottom: 0.1,
              },
              // 蜡烛图类型 'candle_solid'|'candle_stroke'|'candle_up_stroke'|'candle_down_stroke'|'ohlc'|'area'
              type: 'candle_solid',

              area: {
                lineColor: '#FFFFFF',
                style: 'fill',
              },
            },
            yAxis: {
              show: true,
              axisLine: {
                show: true,
                color: '#989fb1',
                size: 1,
              },
            },
            xAxis: {
              show: true,
              axisLine: {
                show: true,
                color: '#989fb1',
                size: 1,
              },
            },
            separator: {
              size: 1,
              color: '#989fb1',
              fill: true,
            },
          },
        })
        this.createIndicators()
        this.chart.setLocale('zh-CN')
        this.chart.applyNewData(this.data)
      },
      createIndicators() {
        // 创建所有可见的指标
        if (this.indicators.MA.visible) {
          this.chart.createIndicator('MA', false, {
            id: 'candle_pane',
          })
        }

        if (this.indicators.VOL.visible) {
          // 在旧版本中，可能需要使用createTechnicalIndicator方法
          this.chart.createIndicator('VOL', true, {
            height: 80,
          })
        }

        if (this.indicators.KDJ.visible) {
          // 在旧版本中，可能需要使用createTechnicalIndicator方法
          this.chart.createIndicator('KDJ', true, {
            height: 80,
          })
        }
      },
      closeIndicator(indicatorName) {
        // 关闭指定的指标
        if (this.indicators[indicatorName]) {
          // 更新指标状态为不可见
          this.indicators[indicatorName].visible = false

          // 完全重新初始化图表，但保留当前数据
          if (this.chart) {
            // 保存当前数据
            const currentData = this.data

            // 销毁当前图表
            dispose(this.chart)

            // 重新初始化图表
            this.initChart()

            // 恢复数据
            this.chart.applyNewData(currentData)
          }
        }
      },
      async loadData() {
        try {
          // 模拟数据，实际使用时替换为真实API
          // 通过远程接口获取K线数据
          const response = await fetch(
            'https://klinecharts.com/datas/kline.json'
          )
          const remoteData = await response.json()
          this.data = remoteData
          this.chart.applyNewData(this.data)
        } catch (error) {
          console.error('加载K线数据失败:', error)
        }
      },
      closeChart() {
        // 触发关闭事件，让父组件处理关闭逻辑
        this.$emit('close')
      },
    },
  }
</script>

<style lang="scss" scoped>
  .k-line-chart-container {
    display: flex;
    flex-direction: column;
    width: 100%;
    height: 500px; /* 高度增加一倍 */
    background-color: #fff;
    border-radius: 4px;

    .chart-header {
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 8px 12px;
      border-bottom: 1px solid #eee;

      .chart-title {
        font-size: 14px;
        font-weight: 500;
      }

      .chart-close {
        color: #909399;
        cursor: pointer;
        &:hover {
          color: #409eff;
        }
      }
    }

    .k-line-chart {
      flex: 1;
      width: 100%;
    }

    .indicators-panel {
      display: flex;
      flex-wrap: wrap;
      padding: 8px;
      border-top: 1px solid #eee;

      .indicator-item {
        display: flex;
        align-items: center;
        padding: 4px 8px;
        margin-right: 12px;
        background-color: #f5f7fa;
        border-radius: 4px;

        .indicator-name {
          margin-right: 4px;
          font-size: 12px;
        }

        .indicator-close {
          font-size: 12px;
          color: #909399;
          cursor: pointer;
          &:hover {
            color: #f56c6c;
          }
        }
      }
    }
  }
</style>
