<template>
  <div class="k-line-chart-container">
    <div class="chart-header">
      <div class="chart-title">K线图</div>
      <div class="chart-close" @click="closeChart">
        <i class="el-icon-close"></i>
      </div>
    </div>
    <div id="chart" ref="kLineChart" class="k-line-chart"></div>
    <div class="indicators-panel">
      <!-- 显示当前可见的指标 -->
      <div v-if="indicators.MA.visible" class="indicator-item">
        <span class="indicator-name">MA</span>
        <i
          class="el-icon-close indicator-close"
          @click="closeIndicator('MA')"
        ></i>
      </div>
      <div v-if="indicators.VOL.visible" class="indicator-item">
        <span class="indicator-name">VOL</span>
        <i
          class="el-icon-close indicator-close"
          @click="closeIndicator('VOL')"
        ></i>
      </div>
      <div v-if="indicators.KDJ.visible" class="indicator-item">
        <span class="indicator-name">KDJ</span>
        <i
          class="el-icon-close indicator-close"
          @click="closeIndicator('KDJ')"
        ></i>
      </div>

      <!-- 添加指标按钮 -->
      <div v-if="hasHiddenIndicators" class="add-indicator-dropdown">
        <el-dropdown trigger="click" @command="openIndicator">
          <span class="add-indicator-btn">
            <i class="el-icon-plus"></i>
            添加指标
          </span>
          <el-dropdown-menu slot="dropdown">
            <el-dropdown-item v-if="!indicators.MA.visible" command="MA">
              MA (移动平均线)
            </el-dropdown-item>
            <el-dropdown-item v-if="!indicators.VOL.visible" command="VOL">
              VOL (成交量)
            </el-dropdown-item>
            <el-dropdown-item v-if="!indicators.KDJ.visible" command="KDJ">
              KDJ (随机指标)
            </el-dropdown-item>
          </el-dropdown-menu>
        </el-dropdown>
      </div>
    </div>
  </div>
</template>

<script>
  import { init, dispose } from 'klinecharts'

  export default {
    name: 'KLineChart',
    props: {
      symbol: {
        type: String,
        default: '000001.SZ',
      },
      interval: {
        type: String,
        default: '1d',
      },
    },
    data() {
      return {
        chart: null,
        data: [],
        indicators: {
          MA: { visible: true, paneId: 'candle_pane' },
          VOL: { visible: false, paneId: 'vol_pane' },
          KDJ: { visible: false, paneId: 'kdj_pane' },
          FISHING_SEASON_V2: { visible: true, paneId: 'fishing_v2_pane' },
        },
      }
    },
    computed: {
      hasHiddenIndicators() {
        return Object.values(this.indicators).some(
          (indicator) => !indicator.visible
        )
      },
    },
    mounted() {
      this.initChart()
      this.loadData()
    },
    beforeDestroy() {
      if (this.chart) {
        dispose(this.chart)
      }
    },
    methods: {
      initChart() {
        this.chart = init(this.$refs.kLineChart, {
          styles: {
            grid: {
              show: false,
              // 网格水平线
              horizontal: {
                show: false,
                size: 1,
                color: '#989fb1',
                style: 'solid',
                dashValue: [2, 2],
              },
              // 网格垂直线
              vertical: {
                show: false,
                size: 1,
                color: '#989fb1',
                style: 'solid',
                dashValue: [2, 2],
              },
            },
            candle: {
              priceMark: {
                high: {
                  color: '#FFFFFF',
                },
                low: {
                  color: '#FFFFFF',
                },
              },
              // 蜡烛图上下间距，大于1为绝对值，大于0小余1则为比例
              margin: {
                top: 0.2,
                bottom: 0.1,
              },
              // 蜡烛图类型 'candle_solid'|'candle_stroke'|'candle_up_stroke'|'candle_down_stroke'|'ohlc'|'area'
              type: 'candle_solid',
              // 蜡烛图颜色配置：红涨绿跌
              bar: {
                upColor: '#ef5350', // 红色（上涨）
                downColor: '#26a69a', // 绿色（下跌）
                noChangeColor: '#888888', // 平盘灰色
                upBorderColor: '#ef5350', // 红色边框
                downBorderColor: '#26a69a', // 绿色边框
                noChangeBorderColor: '#888888', // 平盘灰色边框
                upWickColor: '#ef5350', // 红色芯线
                downWickColor: '#26a69a', // 绿色芯线
                noChangeWickColor: '#888888', // 平盘灰色芯线
              },
              area: {
                lineColor: '#FFFFFF',
                style: 'fill',
              },
            },
            yAxis: {
              show: true,
              axisLine: {
                show: true,
                color: '#989fb1',
                size: 1,
              },
            },
            xAxis: {
              show: true,
              axisLine: {
                show: true,
                color: '#989fb1',
                size: 1,
              },
            },
            separator: {
              size: 1,
              color: '#989fb1',
              fill: true,
            },
            // 指标样式配置：红涨绿跌
            indicator: {
              bars: [
                {
                  upColor: '#ef5350', // 红色（上涨）
                  downColor: '#26a69a', // 绿色（下跌）
                  noChangeColor: '#888888', // 平盘灰色
                },
              ],
              lines: [
                {
                  color: '#FF6B35', // K线颜色（橙红色）
                },
                {
                  color: '#4ECDC4', // D线颜色（青色）
                },
                {
                  color: '#45B7D1', // J线颜色（蓝色）
                },
                {
                  color: '#E11D74', // 第4条线颜色（粉红色）
                },
                {
                  color: '#01C5C4', // 第5条线颜色（青绿色）
                },
              ],
            },
          },
        })
        this.createIndicators()
        this.chart.setLocale('zh-CN')
        this.chart.applyNewData(this.data)
      },
      createIndicators() {
        // 创建所有可见的指标
        if (this.indicators.MA.visible) {
          this.chart.createIndicator('MA', false, {
            id: 'candle_pane',
          })
        }

        if (this.indicators.VOL.visible) {
          // 创建成交量指标，配置红涨绿跌颜色
          this.chart.createIndicator('VOL', true, {
            height: 80,
          })
        }

        if (this.indicators.KDJ.visible) {
          // 创建KDJ指标
          this.chart.createIndicator('KDJ', true, {
            height: 80,
          })
        }
      },
      closeIndicator(indicatorName) {
        // 关闭指定的指标
        if (
          this.indicators[indicatorName] &&
          this.indicators[indicatorName].visible
        ) {
          // 更新指标状态为不可见
          this.$set(this.indicators[indicatorName], 'visible', false)

          // 使用 klinecharts API 移除指标，而不是重新初始化整个图表
          if (this.chart) {
            try {
              // 使用正确的 removeIndicator API
              const result = this.chart.removeIndicator({
                name: indicatorName,
              })

              if (!result) {
                console.warn(`移除指标 ${indicatorName} 失败，可能指标不存在`)
              }
            } catch (error) {
              console.warn(`移除指标 ${indicatorName} 时出错:`, error)
              // 如果 API 调用失败，回退到重新初始化的方法
              this.reinitializeChart()
            }
          }
        }
      },

      reinitializeChart() {
        // 重新初始化图表的备用方法
        if (this.chart) {
          // 保存当前数据
          const currentData = this.data

          // 销毁当前图表
          dispose(this.chart)

          // 重新初始化图表
          this.initChart()

          // 恢复数据
          if (currentData && currentData.length > 0) {
            this.chart.applyNewData(currentData)
          }
        }
      },
      async loadData() {
        try {
          // 模拟数据，实际使用时替换为真实API
          // 通过远程接口获取K线数据
          const response = await fetch(
            'https://klinecharts.com/datas/kline.json'
          )
          const remoteData = await response.json()
          this.data = remoteData
          this.chart.applyNewData(this.data)
        } catch (error) {
          console.error('加载K线数据失败:', error)
        }
      },
      closeChart() {
        // 触发关闭事件，让父组件处理关闭逻辑
        this.$emit('close')
      },

      // 添加重新打开指标的方法
      openIndicator(indicatorName) {
        if (
          this.indicators[indicatorName] &&
          !this.indicators[indicatorName].visible
        ) {
          // 更新指标状态为可见
          this.$set(this.indicators[indicatorName], 'visible', true)

          // 重新创建指标
          if (this.chart) {
            try {
              if (indicatorName === 'MA') {
                this.chart.createIndicator('MA', false, {
                  id: 'candle_pane',
                })
              } else if (indicatorName === 'VOL') {
                // 重新创建成交量指标，使用全局样式配置
                this.chart.createIndicator('VOL', true, {
                  height: 80,
                })
              } else if (indicatorName === 'KDJ') {
                // 重新创建KDJ指标，使用全局样式配置
                this.chart.createIndicator('KDJ', true, {
                  height: 80,
                })
              }
            } catch (error) {
              console.warn(`创建指标 ${indicatorName} 时出错:`, error)
            }
          }
        }
      },

      // 获取指标状态
      getIndicatorStatus() {
        return Object.keys(this.indicators).reduce((status, key) => {
          status[key] = this.indicators[key].visible
          return status
        }, {})
      },
    },
  }
</script>

<style lang="scss" scoped>
  .k-line-chart-container {
    display: flex;
    flex-direction: column;
    width: 100%;
    height: 500px; /* 高度增加一倍 */
    background-color: #fff;
    border-radius: 4px;

    .chart-header {
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 8px 12px;
      border-bottom: 1px solid #eee;

      .chart-title {
        font-size: 14px;
        font-weight: 500;
      }

      .chart-close {
        color: #909399;
        cursor: pointer;
        &:hover {
          color: #409eff;
        }
      }
    }

    .k-line-chart {
      flex: 1;
      width: 100%;
    }

    .indicators-panel {
      display: flex;
      flex-wrap: wrap;
      padding: 8px;
      border-top: 1px solid #eee;

      .indicator-item {
        display: flex;
        align-items: center;
        padding: 4px 8px;
        margin-right: 12px;
        background-color: #f5f7fa;
        border-radius: 4px;

        .indicator-name {
          margin-right: 4px;
          font-size: 12px;
        }

        .indicator-close {
          font-size: 12px;
          color: #909399;
          cursor: pointer;
          &:hover {
            color: #f56c6c;
          }
        }
      }

      .add-indicator-dropdown {
        .add-indicator-btn {
          display: flex;
          align-items: center;
          padding: 4px 8px;
          background-color: #e6f7ff;
          border: 1px dashed #409eff;
          border-radius: 4px;
          color: #409eff;
          font-size: 12px;
          cursor: pointer;
          transition: all 0.3s;

          i {
            margin-right: 4px;
          }

          &:hover {
            background-color: #409eff;
            color: #fff;
            border-color: #409eff;
          }
        }
      }
    }
  }
</style>
