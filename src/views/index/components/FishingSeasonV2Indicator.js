// 捕捞季节指标 V2 - 完全按照通达信代码实现
// 基于您提供的通达信公式：
// WY1001:=(2*CLOSE+HIGH+LOW)/4;
// WY1002:=EMA(WY1001,3);
// WY1003:=EMA(WY1002,3);
// WY1004:=EMA(WY1003,3);
// XYS0:(WY1004-REF(WY1004,1))/REF(WY1004,1)*100;
// STICKLINE(XYS0>=0,XYS0,0,8,0),COLORRED;
// STICKLINE(XYS0<0,XYS0,0,8,0),COLORGREEN;
// PJGJ:=AMOUNT/VOL/100;
// SSRYDJX:=EMA(PJGJ,13);
// SSRCJL:=EMA(VOL,13);
// SSRCJE:=EMA(AMOUNT,13);
// SSRCBJX:=SSRCJE/SSRCJL/100;
// CYS13:=(CLOSE-SSRCBJX)/SSRCBJX*100;
// XYSHSL:=EMA(VOL/CAPITAL*100,13);
// ZZX:0,COLORWHITE;
// STICKLINE(XYSHSL>16.1 AND CYS13>5 ,2 ,0 ,8 ,0 ),COLORGREEN;
// STICKLINE(XYSHSL>13.8 AND CYS13>5 ,1.5 ,0 ,8 ,0 ),COLORYELLOW;
// STICKLINE(XYSHSL>12.1 AND CYS13>5 ,1 ,0 ,8 ,0 ),COLORCYAN;
// STICKLINE(XYSHSL>11.8 AND CYS13>5 ,0.5 ,0 ,8,0 ),COLORBLUE;
// XYS1:EMA(XYS0,2),COLORYELLOW;
// XYS2:EMA(XYS0,1),COLORWHITE;

import { registerIndicator } from 'klinecharts'

export function registerFishingSeasonV2Indicator() {
  registerIndicator({
    name: 'FISHING_SEASON_V2',
    shortName: '捕捞季节V2',
    precision: 2,
    calcParams: [3, 13], // EMA周期3, MA周期13
    shouldOhlc: false,
    shouldFormatBigNumber: false,
    visible: true,
    zLevel: 0,
    series: 'normal',
    figures: [
      // XYS0主力线 - 红绿柱状图
      {
        key: 'xys0',
        title: 'XYS0',
        type: 'bar',
        baseValue: 0,
        styles: (data, indicator) => {
          const indicatorData =
            data?.indicatorData?.[indicator.name] ||
            data?.indicators?.[indicator.name] ||
            {}
          const value = indicatorData.xys0

          if (value === null || value === undefined || isNaN(value)) {
            return {
              color: '#888888',
              borderColor: '#888888',
            }
          }

          // 根据通达信公式：XYS0>=0红色，XYS0<0绿色
          const color = value >= 0 ? '#ef5350' : '#26a69a'
          return {
            color: color,
            borderColor: color,
          }
        },
      },
      // XYS1快线 - 黄色线
      {
        key: 'xys1',
        title: 'XYS1',
        type: 'line',
        styles: () => ({
          color: '#FFD700', // 黄色线 COLORYELLOW
          size: 1,
        }),
      },
      // XYS2慢线 - 白色线
      {
        key: 'xys2',
        title: 'XYS2',
        type: 'line',
        styles: () => ({
          color: '#FFFFFF', // 白色线 COLORWHITE
          size: 1,
        }),
      },
      // ZZX零轴线 - 白色线
      {
        key: 'zzx',
        title: 'ZZX',
        type: 'line',
        styles: () => ({
          color: '#FFFFFF', // 白色零轴线 COLORWHITE
          size: 1,
          style: 'dashed', // 虚线样式
        }),
      },
      // 信号柱状图1 - 绿色 (XYSHSL>16.1 AND CYS13>5)
      {
        key: 'signal1',
        title: '强烈买入',
        type: 'bar',
        baseValue: 0,
        styles: () => ({
          color: '#26a69a', // 绿色 COLORGREEN
          borderColor: '#26a69a',
        }),
      },
      // 信号柱状图2 - 黄色 (XYSHSL>13.8 AND CYS13>5)
      {
        key: 'signal2',
        title: '买入信号',
        type: 'bar',
        baseValue: 0,
        styles: () => ({
          color: '#FFD700', // 黄色 COLORYELLOW
          borderColor: '#FFD700',
        }),
      },
      // 信号柱状图3 - 青色 (XYSHSL>12.1 AND CYS13>5)
      {
        key: 'signal3',
        title: '关注信号',
        type: 'bar',
        baseValue: 0,
        styles: () => ({
          color: '#00FFFF', // 青色 COLORCYAN
          borderColor: '#00FFFF',
        }),
      },
      // 信号柱状图4 - 蓝色 (XYSHSL>11.8 AND CYS13>5)
      {
        key: 'signal4',
        title: '弱买入',
        type: 'bar',
        baseValue: 0,
        styles: () => ({
          color: '#0000FF', // 蓝色 COLORBLUE
          borderColor: '#0000FF',
        }),
      },
    ],
    calc: (kLineDataList, calcParams) => {
      const result = []
      const [emaPeriod, maPeriod] = calcParams

      if (!kLineDataList || kLineDataList.length === 0) return result

      // 辅助函数：计算EMA
      const calculateEMA = (values, period) => {
        const emaValues = []
        const multiplier = 2 / (period + 1)
        let ema = null

        for (let i = 0; i < values.length; i++) {
          const value = values[i]

          if (value === null || value === undefined) {
            emaValues.push(null)
            continue
          }

          if (ema === null) {
            ema = value
          } else {
            ema = (value - ema) * multiplier + ema
          }

          emaValues.push(ema)
        }

        return emaValues
      }

      // 辅助函数：计算REF（引用前N期数据）
      const ref = (values, period) => {
        const refValues = []
        for (let i = 0; i < values.length; i++) {
          if (i < period) {
            refValues.push(null)
          } else {
            refValues.push(values[i - period])
          }
        }
        return refValues
      }

      // 按照通达信公式计算

      // WY1001:=(2*CLOSE+HIGH+LOW)/4;
      const wy1001 = kLineDataList.map((item) => {
        return (2 * item.close + item.high + item.low) / 4
      })

      // WY1002:=EMA(WY1001,3);
      const wy1002 = calculateEMA(wy1001, emaPeriod)

      // WY1003:=EMA(WY1002,3);
      const wy1003 = calculateEMA(wy1002, emaPeriod)

      // WY1004:=EMA(WY1003,3);
      const wy1004 = calculateEMA(wy1003, emaPeriod)

      // XYS0:(WY1004-REF(WY1004,1))/REF(WY1004,1)*100;
      const wy1004Ref = ref(wy1004, 1)
      const xys0 = wy1004.map((val, i) => {
        const refVal = wy1004Ref[i]
        if (val === null || refVal === null || refVal === 0) return null
        return ((val - refVal) / refVal) * 100
      })

      // PJGJ:=AMOUNT/VOL/100; (保留以保持与通达信公式一致性)
      // const pjgj = kLineDataList.map((item) =>
      //   item.volume > 0
      //     ? (item.turnover || item.amount || 0) / item.volume / 100
      //     : 0
      // )

      // SSRYDJX:=EMA(PJGJ,13); (保留以保持与通达信公式一致性)
      // const ssrydjx = calculateEMA(pjgj, maPeriod)

      // SSRCJL:=EMA(VOL,13);
      const volumes = kLineDataList.map((item) => item.volume)
      const ssrcjl = calculateEMA(volumes, maPeriod)

      // SSRCJE:=EMA(AMOUNT,13);
      const amounts = kLineDataList.map(
        (item) => item.turnover || item.amount || 0
      )
      const ssrcje = calculateEMA(amounts, maPeriod)

      // SSRCBJX:=SSRCJE/SSRCJL/100;
      const ssrcbjx = ssrcje.map((amount, i) => {
        const vol = ssrcjl[i]
        return vol > 0 ? amount / vol / 100 : 0
      })

      // CYS13:=(CLOSE-SSRCBJX)/SSRCBJX*100;
      const cys13 = kLineDataList.map((item, i) => {
        const bjx = ssrcbjx[i]
        return bjx > 0 ? ((item.close - bjx) / bjx) * 100 : 0
      })

      // XYSHSL:=EMA(VOL/CAPITAL*100,13);
      // 注意：这里假设CAPITAL为流通股本，实际使用时需要获取真实的流通股本数据
      // 为了演示，我们使用一个估算值
      const hsls = kLineDataList.map((item) => {
        // 假设流通股本为10亿股（实际应该从数据源获取）
        const capital = 1000000000
        return (item.volume / capital) * 100
      })
      const xyshsl = calculateEMA(hsls, maPeriod)

      // XYS1:EMA(XYS0,2);
      const xys1 = calculateEMA(xys0, 2)

      // XYS2:EMA(XYS0,1);
      const xys2 = calculateEMA(xys0, 1)

      for (let i = 0; i < kLineDataList.length; i++) {
        const hsslVal = xyshsl[i] || 0
        const cysVal = cys13[i] || 0

        result.push({
          xys0: xys0[i],
          xys1: xys1[i],
          xys2: xys2[i],
          zzx: 0, // 零轴线
          // 按照通达信公式的信号条件
          signal1: hsslVal > 16.1 && cysVal > 5 ? 2 : null,
          signal2: hsslVal > 13.8 && cysVal > 5 ? 1.5 : null,
          signal3: hsslVal > 12.1 && cysVal > 5 ? 1 : null,
          signal4: hsslVal > 11.8 && cysVal > 5 ? 0.5 : null,
        })
      }

      return result
    },
    createTooltipDataSource: (params) => {
      // 安全检查：确保params存在
      if (!params) return null

      const { indicator, visibleDataList, crosshair } = params

      // 安全检查：确保所有必要参数存在
      if (
        !indicator ||
        !indicator.name ||
        !visibleDataList ||
        !Array.isArray(visibleDataList) ||
        !crosshair ||
        typeof crosshair.dataIndex !== 'number' ||
        crosshair.dataIndex < 0 ||
        crosshair.dataIndex >= visibleDataList.length
      ) {
        return null
      }

      const data = visibleDataList[crosshair.dataIndex]

      if (!data || !data.indicators) return null

      const indicatorData = data.indicators[indicator.name]
      if (!indicatorData) return null

      return {
        name: '捕捞季节V2(通达信)',
        calcParamsText: `(${
          indicator.calcParams && Array.isArray(indicator.calcParams)
            ? indicator.calcParams.join(', ')
            : '3, 13'
        })`,
        legends: [
          {
            title: 'XYS0(主力线)',
            value: indicatorData.xys0
              ? (indicatorData.xys0 >= 0 ? '🔴' : '🟢') +
                indicatorData.xys0.toFixed(2) +
                '%'
              : '--',
          },
          {
            title: 'XYS1(快线)',
            value: indicatorData.xys1
              ? '🟡' + indicatorData.xys1.toFixed(2) + '%'
              : '--',
          },
          {
            title: 'XYS2(慢线)',
            value: indicatorData.xys2
              ? '⚪' + indicatorData.xys2.toFixed(2) + '%'
              : '--',
          },
        ],
      }
    },
  })
}
